import 'package:flutter/material.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/shared_preferences_keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

const Map<String, String> bottomNavigationImages = {
  "home": "assets/bottom_navigation_bar/home.svg",
  "vehicle": "assets/bottom_navigation_bar/vehicle.svg",
  "insights": "assets/bottom_navigation_bar/insights.svg",
  "profile": "assets/bottom_navigation_bar/profile.svg",
  "scan": "assets/bottom_navigation_bar/scan.svg",
  "shadow": "assets/bottom_navigation_bar/shadow.svg",
  "leadership_vehicle" : "assets/bottom_navigation_bar/leadership_vehicle.svg",
};

const Map<String, String> homeScreenImages = {
  "day_mode": "assets/home_screen/day_mode.png",
  "night_mode": "assets/home_screen/night_mode.png",
  "disconnect": "assets/home_screen/disconnect_icon.png",
  "connect": "assets/home_screen/connect_icon.png",
  "distance_icon": "assets/home_screen/distance_icon.png",
  "map_dummy": "assets/home_screen/dummy_map.png",
  "scan_icon": "assets/home_screen/scan_icon.png",
  "fuel_tree_icon": "assets/home_screen/fuel_tree_icon.png",
  "fuel_icon": "assets/home_screen/fuel_icon.png",
  "cluster_icon": "assets/home_screen/cluster_icon.svg",
  "charge_icon": "assets/home_screen/charge_icon.png",
  "bolt_fill": "assets/home_screen/bolt_fill.png",
  "battery_removing_icon": "assets/home_screen/battery_removing_icon.png",
  "battery_unplug": "assets/home_screen/battery_unplug.png",
  "rupee_icon": "assets/home_screen/rupee_icon.png",
  "power": "assets/ride_mode_icons/sports.png",
  "city": "assets/ride_mode_icons/normal.png",
  "eco": "assets/ride_mode_icons/eco.png",
  "charging": "assets/home_screen/chargingVehicle.png",
  "bell_icon": "assets/home_screen/bell_icon.png"
};

const Map<String, String> dashboardImages = {
  "navigation_bar_background":
      "assets/dashbboard/navigation_bar_background.png",
};
const Map<String, String> connectVehicleImages = {
  "password": "assets/connect_vehicle/password.png",
};

const Map<String, String> b2cHomeScreenImages = {
  "vehicle": "assets/b2c_home_screen/vehicle.png",
  "battery_bg": "assets/b2c_home_screen/battery_bg.svg",
  "battery_full": "assets/b2c_home_screen/battery_full.svg",
  "lock": "assets/b2c_home_screen/lock_icon.svg",
  "battery_removed": "assets/b2c_home_screen/battery_removed.svg",
  "connect": "assets/b2c_home_screen/connect_icon.png",
};

const Map<String, String> batteryChargeParcentage = {
  "10": "assets/home_screen/charge_100.png",
  "9": "assets/home_screen/charge_90.png",
  "8": "assets/home_screen/charge_80.png",
  "7": "assets/home_screen/charge_70.png",
  "6": "assets/home_screen/charge_60.png",
  "5": "assets/home_screen/charge_50.png",
  "4": "assets/home_screen/charge_40.png",
  "3": "assets/home_screen/charge_30.png",
  "2": "assets/home_screen/charge_20.png",
  "1": "assets/home_screen/charge_10.png",
  "0": "assets/home_screen/charge_0.png",
};

const Map<String, String> bikeImages = {
  "blue_bike": "assets/bikes/blue_bike.png"
};

const Map<String, String> leadershipImages = {
  "insight_leadership_vehicle": "assets/insights/insight_leadership_vehicle.png",
  "default_vehicle_model": "assets/bikes/blue_bike.png",
  "default_fleet": "assets/bikes/blue_bike.png",
};

const List<List<dynamic>> batteryLevelImages = [
  [Icons.battery_0_bar, colorRedForCharge],
  [Icons.battery_1_bar, colorRedForCharge],
  [Icons.battery_2_bar, colorOrangeForCharge],
  [Icons.battery_3_bar, colorOrangeForCharge],
  [Icons.battery_4_bar, colorYellowForCharge],
  [Icons.battery_5_bar, colorLightGreenForCharge],
  [Icons.battery_6_bar, colorMediumGreenForCharge],
  [Icons.battery_full, colorDarkGreenForCharge],
  [Icons.battery_saver, colorDarkGreenForCharge],
  [Icons.battery_unknown, colorRed600],
];

const Map<String, String> splashScreenImages = {
  "nds_logo_1": "assets/logos/nds_logo_1.png",
  "nds_b2c_logo_1": "assets/logos/nds_b2c_logo_1.png",
  "nds_logo_2": "assets/logos/nds_logo_2.png",
  "lml_logo_1": "assets/logos/lml_logo_1.png",
  "lml_logo_2": "assets/logos/lml_logo_2.png",
  "lml_logo_3": "assets/logos/lml_logo_3.png",
  "lml_logo_4": "assets/logos/lml_logo_4.png",
  "nichesolv_logo_1": "assets/logos/nichesolv_logo_1.png",
  "nichesolv_logo_2": "assets/logos/nichesolv_logo_2.png",
  "nichesolv_logo_3": "assets/logos/nichesolv_logo_3.png",
  "simpson_logo_1": "assets/logos/simpson_logo_1.png",
  "simpson_logo_2": "assets/logos/simpson_logo_2.png",
  "simpson_logo_3": "assets/logos/simpson_logo_3.png",
  "simpson_logo_4": "assets/logos/simpson_logo_4.png",
  "lapa_logo_1": "assets/logos/lapa_logo_1.png",
  "lapa_logo_2": "assets/logos/lapa_logo_2.png",
  "lapa_logo_3": "assets/logos/lapa_logo_3.png",
  "lapa_logo_4": "assets/logos/lapa_logo_4.png",
  "lapa_logo_5": "assets/logos/lapa_logo_5.png",
  "circularWhiteAnimation": "assets/logos/circularGifAnimation.png",
  "nds_Logo_Bg": "assets/logos/ndsLogo.png",
  "nichesolv_Logo_Bg": "assets/logos/nichesolv_logo_1.png",
  "lapa_Logo_Bg": "assets/logos/lapa_logo_1.png",
  "simpson_Logo_Bg": "assets/logos/simpson_logo_1.png",
  "lml_Logo_Bg": "assets/logos/lml_logo_1.png",
};

const Map<String, String> onboardingScreenImages = {
  "battery": "assets/onboarding/onboardBattery.png",
  "performance": "assets/onboarding/onboardingPerformance.png",
};

const Map<String, String> rideHistoryScreenImages = {
  "rideTime": "assets/ride_history/rideHistoryTime.png",
  "rideEngine": "assets/ride_history/rideHistoryEngine.png",
  "rideDistance": "assets/ride_history/rideHistoryDistance.png",
};

const Map<String, String> overViewScreenImages = {
  "testTrip": "assets/overview_screen/test_trip.png",
  "speed": "assets/overview_screen/Speedometer EV.png",
  "charge": "assets/overview_screen/battery_charge.png",
  "speedAnalysis": "assets/overview_screen/speed_analysis.png",
  "mapMarker": "assets/bikes/pin_map_marker.png",
};

const Map<String, String> loaderGifImages = {
  "2Wheels": "assets/loader/loader2wheeler.gif",
  "3Wheels": "assets/loader/loader3wheeler.gif",
};

const Map<String, String> clusterScreenImages = {
  "bike_logo": "assets/cluster/bike_logo.png",
  "bottom_item_1": "assets/cluster/cluster_bottom_menu_1.png",
  "bottom_item_2": "assets/cluster/cluster_bottom_menu_2.png",
  "bottom_item_3": "assets/cluster/cluster_bottom_menu_3.png",
  "bottom_item_4": "assets/cluster/cluster_bottom_menu_4.png",
  "bottom_item_5": "assets/cluster/cluster_bottom_menu_5.png",
  "bottom_item_6": "assets/cluster/cluster_bottom_menu_6.png",
  "bottom_item_7": "assets/cluster/cluster_bottom_menu_7.png",
  "eco_portrait_mode": "assets/cluster/eco_cluster_portrait_mode.png",
  "eco_landscape_mode": "assets/cluster/eco_cluster_landscape_mode.png",
  "city_portrait_mode": "assets/cluster/city_cluster_portrait_mode.png",
  "city_landscape_mode": "assets/cluster/city_cluster_landscape_mode.png",
  "power_portrait_mode": "assets/cluster/power_cluster_portrait_mode.png",
  "power_landscape_mode": "assets/cluster/power_cluster_landscape_mode.png",
  "reverse_portrait_mode": "assets/cluster/reverse_cluster_portrait_mode.png",
  "reverse_landscape_mode": "assets/cluster/reverse_cluster_landscape_mode.png",
  "voltage_icon": "assets/cluster/cluster_voltage_icon.png",
  "temp_icon": "assets/cluster/cluster_thermostate_icon.png",
  "range_icon": "assets/cluster/cluster_range_icon.png",
  "battery_icon": "assets/cluster/cluster_battery_icon.png",
  "no_battery_icon": "assets/cluster/cluster_no_battery_icon.png",
  "no_voltage_icon": "assets/cluster/cluster_no_voltage_icon.png",
  "red_trip_meter_button": "assets/cluster/red_trip_meter_button.png",
  "green_trip_meter_button": "assets/cluster/green_trip_meter_button.png",
};

const Map<String, String> loginScreenImages = {
  "loginScreenImage": "assets/login/loginScreenImage.png",
  "loginScreenImageGreen": "assets/login/loginScreenImageGreen.png",
  "exclamationMark": "assets/login/errorExclamationMark.png",
  "loginScreenImageBlue": "assets/login/loginScreenImageBlue.png",
  "riderToggleButtonIcon": "assets/login/riderToggleButtonIcon.png",
  "watcherToggleButtonIcon": "assets/login/watcherToggleButtonIcon.png",
  "loginScreenImageLapa": "assets/login/loginScreenImageLapa.png",
};

const Map<String, String> insightsScreenImages = {
  "vertical_divider": "assets/insights/vertical_divider.png",
  "expand_widget_icon": "assets/insights/expand_widget_icon.png",
  "battery_bolt": "assets/insights/battery_bolt.png",
  "distance_traveled": "assets/insights/distance_traveled.png",
  "tachometer_alt_slowest": "assets/insights/tachometer_alt_slowest.png",
  "tachometer_alt_fastest": "assets/insights/tachometer_alt_fastest.png",
  "curve_arrow": "assets/insights/curve_arrow.png",
  "battery_swap": "assets/insights/battery_swap.png",
  "time_forward": "assets/insights/time_forward.png",
  "distance_travelled_image": "assets/insights/distance_travelled_image.png",
  "ride_duration_image": "assets/insights/ride_duration_image.png"
};
const Map<String, String> profileScreenImages = {
  "menu1": "assets/profile/profile.svg",
  "menu2": "assets/profile/documents.svg",
  "menu3": "assets/profile/settings.svg",
  "menu4": "assets/profile/help.svg",
  "menu5": "assets/profile/logout.svg",
  "menu6": "assets/profile/about_vehicle.svg",
  "menu7": "assets/profile/access.svg",
  "menu9": "assets/profile/emergency_sos.svg",
  "contact1": "assets/profile/call_icon.png",
  "contact2": "assets/profile/mail_icon.png",
  "addRider": "assets/profile/add_rider.png",
  "family": "assets/profile/family.png",
  "friend": "assets/profile/friend.png",
  "relative": "assets/profile/relative.png",
  "others": "assets/profile/others.png",
  "invite_sent": "assets/profile/invite_sent.png",
  "rider_edit_drop_down": "assets/profile/rider_edit_drop_down.png",
  "edit": "assets/profile/edit.png",
  "delete": "assets/profile/delete.png",
  "profileBg": "assets/profile/profile_Bg.png",
  "user_profile_placeholder": "assets/profile/user_profile_placeholder.png",
};

const Map<String, String> vehicleDetailScreenImages = {
  "vehicle_base": "assets/vehicle_detail/vehicle_base.svg",
  "battery_icon": "assets/vehicle_detail/battery_icon.svg",
  "ic_siren" : "assets/vehicle_detail/ic_siren.svg",
  "ic_warn" : "assets/vehicle_detail/ic_warn.svg",
};

const Map<String, String> alertImages = {
  "alert_icon": "assets/alert/alert_icon.png",
  "no_alert_icon": "assets/alert/no_alert_icon.png",
  "thermometer_green": "assets/alert/thermometer_green.png",
  "thermometer_red": "assets/alert/thermometer_red.png",
};

const Map<String, String> noInternetConnectionImages = {
  "no_internet": "assets/connectivity/no_internet.png"
};

const Map<String, String> googleMapImages = {
  "marker_green": "assets/google_map/marker_green.png",
  "marker_red": "assets/google_map/marker_red.png"
};




// Global variable to store leadership status
bool isProdRedLeadershipValue = false;

/// Initialize the leadership value from SharedPreferences
Future<void> initializeIsProdRedLeadership() async {
  final prefs = await SharedPreferences.getInstance();
  isProdRedLeadershipValue = prefs.getBool(isProdRedLeadershipKey) ?? false;
}

String getVehicleIcon() {
  bool isLeadership = isProdRedLeadershipValue;
  return isLeadership
      ? bottomNavigationImages['leadership_vehicle']!
      : bottomNavigationImages['vehicle']!;
}
