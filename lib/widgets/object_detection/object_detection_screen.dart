import 'package:flutter/material.dart';
import 'package:ultralytics_yolo/ultralytics_yolo.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';

class ObjectDetectionScreen extends StatefulWidget {
  const ObjectDetectionScreen({Key? key}) : super(key: key);

  @override
  State<ObjectDetectionScreen> createState() => _ObjectDetectionScreenState();
}

class _ObjectDetectionScreenState extends State<ObjectDetectionScreen> {
  List<YOLOResult> _detections = [];
  String _statusMessage = 'Ready for detection';
  int _detectionCount = 0;
  bool _isDetecting = false;

  @override
  void initState() {
    super.initState();
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_name': 'Object Detection Screen',
      'screen_class': widget.runtimeType.toString()
    });
  }

  void _onResult(List<YOLOResult> results) {
    setState(() {
      _detections = results;
      _detectionCount = results.length;
      _statusMessage = results.isEmpty
          ? 'No objects detected'
          : 'Detected ${results.length} objects';
    });
  }

  Widget _buildDetectionInfo() {
    if (_detections.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.8),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Detected Objects (${_detections.length}):',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          ..._detections.take(5).map((detection) {
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 2),
              child: Text(
                '• ${detection.className}: ${(detection.confidence * 100).toStringAsFixed(1)}%',
                style: const TextStyle(color: Colors.white, fontSize: 14),
              ),
            );
          }).toList(),
          if (_detections.length > 5)
            Text(
              '... and ${_detections.length - 5} more',
              style: const TextStyle(color: Colors.grey, fontSize: 12),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text(
          'Live Object Detection',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.black,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 16),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: _isDetecting ? Colors.green : Colors.grey,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              _isDetecting ? 'LIVE' : 'READY',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: Stack(
        children: [
          // YOLO Camera View
          Positioned.fill(
            child: YOLOView(
              modelPath: 'yolo11n', // Using YOLO11 nano model
              task: YOLOTask.detect,
              onResult: _onResult,
              confidenceThreshold: 0.4,
              iouThreshold: 0.4,
            ),
          ),

          // Status overlay at the top
          Positioned(
            top: 16,
            left: 16,
            right: 16,
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Status: $_statusMessage',
                    style: const TextStyle(color: Colors.white, fontSize: 14),
                  ),
                  if (_detectionCount > 0)
                    Text(
                      'Total objects: $_detectionCount',
                      style: const TextStyle(color: Colors.green, fontSize: 14),
                    ),
                ],
              ),
            ),
          ),

          // Detection info panel at the bottom
          Positioned(
            bottom: 16,
            left: 0,
            right: 0,
            child: _buildDetectionInfo(),
          ),

          // Instructions overlay (shown when no detections)
          if (_detections.isEmpty)
            Positioned(
              bottom: 100,
              left: 16,
              right: 16,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.8),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.camera_alt,
                      color: Colors.white,
                      size: 32,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Point your camera at objects to detect them',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'The AI will automatically identify and label objects in real-time',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
