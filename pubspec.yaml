name: nds_app
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.1+1

environment:
  sdk: ">=2.19.6 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  syncfusion_flutter_gauges: ^29.1.37
  cupertino_icons: ^1.0.2
  http: ^1.2.2
  shared_preferences: ^2.2.0
  google_maps_flutter: ^2.5.3
  flutter_launcher_icons: ^0.14.3
  fluttertoast: ^8.2.12
  flutter_svg: ^2.0.7
  intl: ^0.18.1
  location: ^5.0.3
  wakelock_plus: ^1.2.11
  image_picker: ^1.0.4
  http_parser: ^4.0.2
  carousel_slider: ^5.0.0
  smooth_page_indicator: ^1.1.0
  syncfusion_flutter_charts: ^29.1.37+1
  firebase_analytics: ^10.7.4
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.9
  flutter_reactive_ble: 5.4.0
  firebase_core_platform_interface: ^5.4.0
  permission_handler: ^11.4.0
  mobile_scanner: ^7.0.1
  vibration: ^3.1.3
  flutter_local_notifications: ^19.0.0
  connectivity_plus: ^5.0.2
  firebase_remote_config: ^4.3.8
  package_info_plus: ^8.0.0
  open_store: ^0.5.0
  webview_flutter: 4.10.0
  equatable: ^2.0.5
  flutter_bloc: ^8.1.6
  bloc: ^8.1.4
  firebase_crashlytics: ^3.4.8
  synchronized: ^3.0.0+2
  flutter_native_contact_picker: ^0.0.10
  url_launcher: ^6.3.1
  share_plus: ^10.0.0
  auto_size_text: ^3.0.0
  cached_network_image: ^3.4.1
  path_provider: ^2.1.2
  uuid: ^4.1.0
  ultralytics_yolo: ^0.1.19
  camera: ^0.10.5+9

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0

flutter_icons:
  image_path: "assets/logos/lml_logo_5.png"
  android: true
  ios: true

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  assets:
    - assets/
    - assets/bottom_navigation_bar/
    - assets/b2c_home_screen/
    - assets/home_screen/
    - assets/ride_mode_icons/
    - assets/bikes/
    - assets/battery_level/
    - assets/logos/
    - assets/cluster/
    - assets/login/
    - assets/dummy/
    - assets/insights/
    - assets/profile/
    - assets/vehicle_detail/
    - assets/alert/
    - assets/connectivity/
    - assets/google_map/
    - assets/connect_vehicle/
    - assets/onboarding/
    - assets/loader/
    - assets/ride_history/
    - assets/overview_screen/
    - assets/range_screen/
    - assets/model/
    
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Poppins
      fonts:
        - asset: fonts/Poppins-Regular.ttf

  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
